# Firebase Integration for Doc-Scanner

This module provides Firebase Firestore integration for user authentication and document management.

## Setup

### 1. Firebase Project Setup
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing project
3. Enable Firestore Database
4. Go to Project Settings > Service Accounts
5. Generate a new private key (downloads JSON file)

### 2. Environment Configuration

#### For Local Development:
1. Copy the downloaded service account JSON file to `BACKEND/MyFunctionApp/`
2. Rename it to `firebase-service-account.json`
3. Add to `.env` file:
```
FIREBASE_SERVICE_ACCOUNT_PATH=firebase-service-account.json
```

#### For Azure Deployment:
1. Convert the JSON file content to a single line string
2. Add to Azure Function App Settings:
```
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your-project-id",...}
```

### 3. Firestore Database Structure

#### Users Collection (`users`)
```json
{
  "id": "auto-generated-id",
  "username": "<PERSON>",
  "email": "<EMAIL>",
  "profile_url": "https://example.com/profile.jpg",
  "documents": [
    {
      "document_id": "doc_20241201_143022",
      "type": "image-to-csv",
      "original_filename": "table.jpg",
      "output_filename": "abc123_processed.csv",
      "file_id": "abc123",
      "processing_type": "image_processing",
      "processed_at": "2024-12-01T14:30:22Z"
    }
  ],
  "created_at": "2024-12-01T10:00:00Z",
  "last_login": "2024-12-01T14:30:22Z",
  "updated_at": "2024-12-01T14:30:22Z"
}
```

## API Endpoints

### 1. Fetch User Data
```
GET /api/fetchUserData?email=<EMAIL>
```

### 2. Register/Update User
```
POST /api/registerUser
{
  "email": "<EMAIL>",
  "username": "John Doe",
  "profile_url": "https://example.com/profile.jpg"
}
```

### 3. Get User Documents
```
GET /api/getUserDocuments?email=<EMAIL>
```

### 4. Add Document to User
```
POST /api/addDocumentToUser
{
  "email": "<EMAIL>",
  "document_data": {
    "type": "image-to-csv",
    "original_filename": "table.jpg",
    "output_filename": "abc123_processed.csv",
    "file_id": "abc123",
    "processing_type": "image_processing"
  }
}
```

## Security Rules

Add these Firestore security rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.token.email == resource.data.email;
    }
  }
}
```

## Functions Integration

The document processing functions (`process/image-to-csv`, `process/pdf-to-csv`) automatically save processed documents to the user's Firebase record if an `email` parameter is included in the request body.

Example:
```json
{
  "file_id": "abc123",
  "email": "<EMAIL>"
}
```
