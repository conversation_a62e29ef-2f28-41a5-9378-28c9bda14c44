import firebase_admin
from firebase_admin import credentials, firestore
import os
import logging
from datetime import datetime
import json

# Initialize Firebase Admin SDK
def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        # Check if Firebase is already initialized
        if not firebase_admin._apps:
            # Try to get service account key from environment variable
            service_account_key = os.environ.get('FIREBASE_SERVICE_ACCOUNT_KEY')
            
            if service_account_key:
                # Parse the JSON string
                service_account_info = json.loads(service_account_key)
                cred = credentials.Certificate(service_account_info)
            else:
                # Fallback to service account file (for local development)
                service_account_path = os.environ.get('FIREBASE_SERVICE_ACCOUNT_PATH', 'firebase-service-account.json')
                if os.path.exists(service_account_path):
                    cred = credentials.Certificate(service_account_path)
                else:
                    # Use default credentials (for Google Cloud deployment)
                    cred = credentials.ApplicationDefault()
            
            firebase_admin.initialize_app(cred)
            logging.info("Firebase Admin SDK initialized successfully")
        
        return firestore.client()
    except Exception as e:
        logging.error(f"Failed to initialize Firebase: {str(e)}")
        raise

# Initialize Firestore client
db = initialize_firebase()

def fetch_user_by_email(email):
    """Fetch user data by email from Firestore"""
    try:
        # Query users collection by email
        users_ref = db.collection('users')
        query = users_ref.where('email', '==', email).limit(1)
        docs = query.stream()
        
        for doc in docs:
            user_data = doc.to_dict()
            user_data['id'] = doc.id
            logging.info(f"User found: {email}")
            return {
                "success": True,
                "user": user_data,
                "exists": True
            }
        
        logging.info(f"User not found: {email}")
        return {
            "success": True,
            "user": None,
            "exists": False,
            "message": "User not found"
        }
        
    except Exception as e:
        logging.error(f"Error fetching user by email {email}: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "exists": False
        }

def store_google_user(username, email, profile_url, documents=None):
    """Store or update Google user in Firestore"""
    try:
        if documents is None:
            documents = []
            
        # Check if user already exists
        existing_user = fetch_user_by_email(email)
        
        if existing_user["exists"]:
            # User exists, update their information
            user_id = existing_user["user"]["id"]
            user_ref = db.collection('users').document(user_id)
            
            update_data = {
                'username': username,
                'profile_url': profile_url,
                'last_login': datetime.now(),
                'updated_at': datetime.now()
            }
            
            user_ref.update(update_data)
            logging.info(f"User updated: {email}")
            
            return {
                "success": True,
                "exists": True,
                "user_id": user_id,
                "message": "User updated successfully"
            }
        else:
            # Create new user
            user_data = {
                'username': username,
                'email': email,
                'profile_url': profile_url,
                'documents': documents,
                'created_at': datetime.now(),
                'last_login': datetime.now(),
                'updated_at': datetime.now()
            }
            
            # Add user to Firestore
            doc_ref = db.collection('users').add(user_data)
            user_id = doc_ref[1].id
            
            logging.info(f"New user created: {email}")
            
            return {
                "success": True,
                "exists": False,
                "user_id": user_id,
                "message": "User created successfully"
            }
            
    except Exception as e:
        logging.error(f"Error storing Google user {email}: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "exists": False
        }

def add_document_to_user(email, document_data):
    """Add a processed document to user's document list"""
    try:
        user = fetch_user_by_email(email)
        
        if not user["exists"]:
            return {
                "success": False,
                "error": "User not found"
            }
        
        user_id = user["user"]["id"]
        user_ref = db.collection('users').document(user_id)
        
        # Add document with timestamp
        document_entry = {
            **document_data,
            'processed_at': datetime.now(),
            'document_id': f"doc_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        
        # Update user's documents array
        user_ref.update({
            'documents': firestore.ArrayUnion([document_entry]),
            'updated_at': datetime.now()
        })
        
        logging.info(f"Document added to user {email}")
        
        return {
            "success": True,
            "message": "Document added successfully",
            "document_id": document_entry['document_id']
        }
        
    except Exception as e:
        logging.error(f"Error adding document to user {email}: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

def get_user_documents(email):
    """Get all documents for a user"""
    try:
        user = fetch_user_by_email(email)
        
        if not user["exists"]:
            return {
                "success": False,
                "error": "User not found"
            }
        
        documents = user["user"].get("documents", [])
        
        return {
            "success": True,
            "documents": documents,
            "count": len(documents)
        }
        
    except Exception as e:
        logging.error(f"Error getting documents for user {email}: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }
